需求背景
天赐的作业表单出现了一些关联逻辑，
- MOC选是，才展现MOC登记号 的输入， 且必填
- 高风险作业选是， 才展现 作业方案登记号， 且必填
- 挂牌、锁定、隔离 选适用，  才展现底下方框的内容，且必填

方案描述
作业票表单模版修改
- 修改作业模版， 每个基础组件新都新增2个属性， 类似于 是否必填   这个属性
  - 一个叫依赖选项(dependent)， 是当前表单页面里单选的元素，标志这个元素使用该元素的itemId,  使用形式单选最好，输入框也能接受
  - 一个叫依赖选项值(dependentValue)，给出候选的选项值， 必须是个数字(单选目前的key都是数字），使用形式优选单选， 输入框也能接受
- 每次提交时，检查每个元素的依赖项是否存在，对应的选项值是否存在，不存在不允许提交


作业开票的逻辑
- 任何表单模版的json串，原来是直接渲染，现在多一层过滤逻辑
  - 如果该元素的json串的dependent属性不为空，则默认不展示，除非在页面上发现这个元素依赖元素(单选)当前的值和设定的 dependentValue一致，才呈现。 呈现后的是否必填属性和原来一致，只取决于元素本身的isReq属性。
- 极端情况：
  - 依赖选项满足了条件，相关的选项也选了值
  - 更改了依赖选项的选择值，这些有依赖的组件自动消失，结果也不上报

示例说明：
- 作业的表单模板，每个元素都会新增两个属性dependent 和 dependentValue
- 下属中
  - 第一个元素dependent为空，直接渲染。
  - 第二个元素dependent不为空默认不渲染，则它需要寻找页面里itemId值为 formId-8R8E64yP 的元素，当它的取值是 2 的时候才渲染
 [
 {
        "compId": "5",
        "itemId": "formId-8R8E64yP",
        "business": "unitCategory",
        "children": [],
        "compName": "单选框",
        "compType": "radio",
        "formData": {
                "isReq": "required",
                "dependent": "",
                "dependentValue": 0, 
                "isPrint": true,
                "options": "",
                "formName": "作业单位类别",
                "candidateList": [{
                        "id": 1,
                        "label": "本厂"
                }, {
                        "id": 2,
                        "label": "承包商"
                }]
        },
        "nodeIndex": 5
}, 
{
        "compId": "10-1",
        "itemId": "formId-content",
        "business": "workContent",
        "children": [],
        "compName": "作业内容",
        "compType": "input",
        "formData": {
                "type": "string",
                "isReq": "required",
                "dependent": "formId-8R8E64yP",
                "dependentValue": 2, 
                "isPrint": true,
                "disabled": true,
                "formName": "作业内容",
                "defaultText": null,
                "placeHolder": null,
                "serviceRange": [1]
        },
        "nodeIndex": 0
},
]