import {
  IconDownload,
  IconPlus,
  IconPrint,
  IconRefresh,
  IconSetting,
  IconUpload,
} from "@douyinfe/semi-icons";
import {
  Button,
  ButtonGroup,
  Popconfirm,
  Table,
  Toast,
  Tooltip,
} from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  delContractorEmployeeCertificate,
  delContractorEmployeeCertificates,
  getContractorEmployeeCertificateList,
} from "api";
import { jobCertificatesReferValues } from "atoms";
import {
  contractorEmployeeCertificateColumnsAtom,
  contractorEmployeeCertificateConfigModalAtom,
  contractorEmployeeCertificateEditModal,
  contractorEmployeeCertificateFilterAtom,
  contractorEmployeeCertificateFnAtom,
} from "atoms/basicInfo";
import { TableConfig, UploadTmpl } from "components";
import dayjs from "dayjs";
import { use<PERSON>tom, useAtomValue } from "jotai";
import { map, prop } from "ramda";
import { FC, useCallback, useEffect, useMemo, useState } from "react";

type ContractorCertificateContentProps = {
  mode?: "modal" | "select";
  cb?: (record: number[], type: 1 | 2) => void;
  initData?: (type: 1 | 2, record: any[]) => void;
  initRows?: any[];
  filter: Object;
  readonly?: boolean;
};

export const ContractorEmployeeCertificateContent: FC<
  ContractorCertificateContentProps
> = ({ mode, cb, initData, initRows, filter = {}, readonly = false }) => {
  const queryClient = useQueryClient();
  const [rows, setRows] = useState<number[]>([]);
  const [contractorCertificateFilter, setCertificateFilter] = useAtom(
    contractorEmployeeCertificateFilterAtom
  );
  const [contractorCertificateFn, setCertificateFn] = useAtom(
    contractorEmployeeCertificateFnAtom
  );
  const [contractorCertificateEdit, setCertificateEdit] = useAtom(
    contractorEmployeeCertificateEditModal
  );
  const [configModal, setShow] = useAtom(
    contractorEmployeeCertificateConfigModalAtom
  );
  const [_columns, setColumns] = useAtom(
    contractorEmployeeCertificateColumnsAtom
  );
  const jobCertificatesReferValue = useAtomValue(jobCertificatesReferValues);

  const { isLoading, data, refetch } = useQuery({
    queryKey: [
      "getContractorEmployeeCertificateList",
      contractorCertificateFilter,
      filter,
    ],
    queryFn: () =>
      getContractorEmployeeCertificateList({
        ...contractorCertificateFilter,
        filter: { ...filter, ...contractorCertificateFilter.filter },
      }),
  });

  const mutation = useMutation({
    mutationFn: delContractorEmployeeCertificate,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      queryClient.invalidateQueries({
        queryKey: ["getContractorEmployeeCertificateList"],
      });
      Toast.success(opts);
      contractorCertificateFn?.refetch?.();
    },
  });

  const removes = useMutation({
    mutationFn: delContractorEmployeeCertificates,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      Toast.success(opts);
      queryClient.invalidateQueries({
        queryKey: ["getContractorEmployeeCertificateList"],
      });
      contractorCertificateFn?.refetch?.();
    },
  });

  useEffect(() => {
    if (data) {
      // 使用 setTimeout 确保在下一个事件循环中调用回调，避免渲染期间的状态更新
      setTimeout(() => {
        cb?.(rows, 2);
      }, 0);
    }
  }, [rows, data]);

  useEffect(() => {
    if (data) {
      initData?.(2, data?.data?.results ?? []);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const dataSource = useMemo(() => data?.data ?? {}, [data?.data]);
  const result = useMemo(() => dataSource?.results ?? [], [dataSource]);

  const handleConfirm = useCallback(
    (record) => {
      mutation.mutate(record.id);
    },
    [mutation]
  );

  const rowSelection = useMemo(
    () => ({
      fixed: true,
      onChange: (selectedRowKeys: any[]) => {
        setRows(selectedRowKeys);
      },
      getCheckboxProps: (record) => ({
        disabled:
          mode === "modal" && dayjs(record.expireDate).isBefore(dayjs()),
      }),
      selectedRowKeys: rows?.length ? rows : map(prop("id"), initRows ?? []),
    }),
    [initRows, rows, mode]
  );

  const handleOpenSetting = useCallback(() => {
    setShow(true);
  }, [setShow]);

  const handleOpenEdit = useCallback(
    (id?: string) => {
      setCertificateEdit({
        id: id ?? "",
        show: true,
      });
    },
    [setCertificateEdit]
  );

  const handlePageChange = useCallback(
    (currentPage: number) => {
      setCertificateFilter({
        ...contractorCertificateFilter,
        pageNumber: currentPage,
      });
    },
    [contractorCertificateFilter, setCertificateFilter]
  );

  const handlePageSizeChange = useCallback(
    (pageSize: number) => {
      setCertificateFilter({
        ...contractorCertificateFilter,
        pageSize: pageSize,
      });
    },
    [contractorCertificateFilter, setCertificateFilter]
  );

  const handleRemoves = useCallback(() => {
    removes.mutate(rows);
    setRows([]);
  }, [removes, rows, setRows]);

  const handleCallback = (record) => {
    cb?.(record);
  };

  const columns = useMemo(() => {
    if (mode == "modal") {
      return _columns;
    }
    if (mode == "select") {
      return [
        ..._columns,
        {
          title: <Tooltip content="操作">操作</Tooltip>,
          isShow: true,
          dataIndex: "operate",
          key: "operate",
          align: "center",
          width: 150,
          render: (text, record) => (
            <ButtonGroup aria-label="操作按钮组">
              <Button
                onClick={() => {
                  handleCallback(record);
                }}
                disabled={dayjs(record.expireDate).isBefore(dayjs())}
              >
                使用该项
              </Button>
            </ButtonGroup>
          ),
        },
      ];
    }
    return [
      ..._columns,
      readonly
        ? {}
        : {
            title: <Tooltip content="操作">操作</Tooltip>,
            isShow: true,
            dataIndex: "operate",
            key: "operate",
            align: "center",
            width: 150,
            render: (text, record) => (
              <div>
                <ButtonGroup aria-label="操作按钮组">
                  <Button
                    onClick={() => {
                      handleOpenEdit(record.id);
                    }}
                  >
                    编辑
                  </Button>
                  <Popconfirm
                    position="left"
                    title="确定是否要删除该项？"
                    content="此修改将不可逆"
                    okType="danger"
                    okButtonProps={{
                      className:
                        "semi-button semi-button-danger semi-button-light",
                    }}
                    onConfirm={() => {
                      handleConfirm(record);
                    }}
                  >
                    <Button type="danger">删除</Button>
                  </Popconfirm>
                </ButtonGroup>
              </div>
            ),
          },
    ];
  }, [_columns, mode, handleCallback]);

  return (
    <>
      <TableConfig
        visible={configModal}
        columns={_columns}
        handleClose={setShow}
        handleSave={setColumns}
      />
      <div className="bg-white shadow px-4 h-fit rounded big_screen_table_box">
        <div className="flex py-4 justify-between big_screen_table_filter_operation">
          {mode == "select" || mode == "modal" ? (
            <div></div>
          ) : (
            <div className="flex gap-4">
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={() => {
                  handleOpenEdit();
                }}
              >
                新增
                <IconPlus size="small" />
              </button>
              <span className="flex text-gray-900 text-opacity-60 text-sm justify-center items-center">
                已选中{rows?.length ?? 0}个
              </span>
              {rows?.length ? (
                <Popconfirm
                  title="确定是否要删除该项？"
                  content="此修改将不可逆"
                  okType="danger"
                  okButtonProps={{
                    className:
                      "semi-button semi-button-danger semi-button-light",
                  }}
                  onConfirm={handleRemoves}
                >
                  <button className="btn btn-sm rounded">批量删除</button>
                </Popconfirm>
              ) : null}
            </div>
          )}

          <div className="flex gap">
            <div className="tooltip" data-tip="刷新">
              <button
                className="btn btn-sm btn-ghost rounded no-animation"
                onClick={() => {
                  refetch();
                }}
              >
                <IconRefresh />
              </button>
            </div>
            <UploadTmpl
              entity={"承包商人员证书"}
              excelType={"contractor_employee_certificate_template"}
              downUrl={encodeURI(
                "/static/template/承包商人员证书信息导入模板.xlsx"
              )}
              tip={
                "请先准备好对应的承包商/员工/证书类型/发证机关信息，否则导入会失败"
              }
            />
            <div className="tooltip" data-tip="下载">
              <button className="btn btn-sm btn-ghost rounded no-animation">
                <IconDownload />
              </button>
            </div>
            <div className="tooltip" data-tip="上传">
              <button className="btn btn-sm btn-ghost rounded no-animation">
                <IconUpload />
              </button>
            </div>

            <div className="tooltip" data-tip="打印">
              <button className="btn btn-sm btn-ghost rounded no-animation">
                <IconPrint />
              </button>
            </div>

            <div className="tooltip" data-tip="设置">
              <button
                className="btn btn-sm btn-ghost rounded no-animation"
                onClick={handleOpenSetting}
              >
                <IconSetting />
              </button>
            </div>
          </div>
        </div>
        <Table
          className="rounded overflow-hidden"
          rowKey="id"
          // scroll={{ x:1200 }}
          columns={columns?.filter?.((o) => o.fixed || o.isShow)}
          dataSource={result}
          rowSelection={mode === "select" || readonly ? false : rowSelection}
          loading={isLoading}
          onHeaderRow={(columns, index) => {
            return {
              className: "text-gray-900 text-opacity-90 bg-gray-50",
            };
          }}
          headerStyle={{ color: "blue" }}
          pagination={{
            showSizeChanger: true,
            popoverPosition: "topRight",
            currentPage: dataSource?.pageNumber ?? 1,
            pageSize: dataSource?.pageSize ?? 10,
            total: dataSource?.totalCount ?? 0,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
            pageSizeOpts: [10, 15, 20, 50],
          }}
        />
      </div>
    </>
  );
};
