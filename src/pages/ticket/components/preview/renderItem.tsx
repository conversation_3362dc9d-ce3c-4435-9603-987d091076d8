import { Form, Tooltip, useFormApi } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getRiskMeasureList } from "api";
import { certificateSelectAtom, mapPickerAtom } from "atoms";
import {
  AreaSearch,
  ContractorSearch,
  DepartmentPicker,
  EmployeePicker,
  EmployeeSearch,
  MapPicker,
  RISK_MEASURE_ACCIDENTTYPE,
  Upload,
} from "components";
import { useAtom } from "jotai";
import { find, pick, propEq, remove } from "ramda";
import { FC, useMemo, useState } from "react";
import { RenderTable } from "./renderTable";

type HighWorkHeightRuleList = {
  /**
   * 高处作业级别
   */
  highLevel: number;
  rangeRuleList: RangeRuleList[];
};

type RangeRuleList = {
  /**
   * 比较运算法
   */
  operator: number;
  /**
   * 阈值
   */
  pivotNumber: number;
};

type RenderItemProps = {
  item: any;
  k: number;
  isTable?: boolean;
  isHighWork?: boolean;
  rule: HighWorkHeightRuleList[];
};

export const RenderItem: FC<RenderItemProps> = ({
  item,
  k,
  isTable = false,
  isHighWork = false,
  rule = [],
}) => {
  const formApi = useFormApi();
  const [visible, setVisible] = useState(false);
  const [atom, setAtom] = useAtom(certificateSelectAtom);
  const [mapPicker, setMapPicker] = useAtom(mapPickerAtom);

  const { data: riskMeasureData } = useQuery({
    queryKey: ["riskMeasureList"],
    queryFn: () => {
      return getRiskMeasureList({
        pageNumber: 1,
        pageSize: 999,
        filter: {},
      });
    },
  });

  const riskMeasureOptions = useMemo(() => {
    return riskMeasureData?.data?.results ?? [];
  }, [riskMeasureData]);

  const name = item?.formData?.formName ?? item?.compName ?? "未命名";

  const field = `form.${item?.business ? item?.business : item.itemId}`;
  const isInputNumber = Boolean((item?.formData?.type ?? "") === "float");

  const asyncValidate = (val, values) => {
    if (!isHighWork) {
      return;
    }
    const { level, isUpgrade } = values.form;
    if (!level) {
      return "请先选择高处作业级别";
    }
    /* if (!isUpgrade) {
      return "请先选择是否升级";
    } */
    let ruleItem: HighWorkHeightRuleList;
    rule.forEach((o: HighWorkHeightRuleList) => {
      if (o.highLevel === level) {
        ruleItem = o;
      }
    });

    if (ruleItem && ruleItem.rangeRuleList?.length) {
      for (const rangeRule of ruleItem.rangeRuleList) {
        const { operator, pivotNumber } = rangeRule;

        // 如果 isUpgrade === 2，只检查上限，跳过下限检查
        if (isUpgrade === 2 && (operator === 2 || operator === 4)) {
          continue;
        }

        switch (operator) {
          case 1: // 小于
            if (!(val < pivotNumber)) {
              return `高度必须小于 ${pivotNumber}米`;
            }
            break;
          case 2: // 大于
            if (!(val > pivotNumber)) {
              return `高度必须大于 ${pivotNumber}米`;
            }
            break;
          case 3: // 小于等于
            if (!(val <= pivotNumber)) {
              return `高度必须小于等于 ${pivotNumber}米`;
            }
            break;
          case 4: // 大于等于
            if (!(val >= pivotNumber)) {
              return `高度必须大于等于 ${pivotNumber}米`;
            }
            break;
          case 5: // 等于
            if (!(val === pivotNumber)) {
              return `高度必须等于 ${pivotNumber}米`;
            }
            break;
          default:
            return "无效的操作符";
        }
      }
    }

    return undefined; // 验证通过
  };

  const handleRemove = (field, idx, formApi) => {
    const fieldMap = {
      "form.guardianInCharge-name": {
        mainField: "form.guardianInCharge",
        relatedField: "form.guardianCertificate",
        relatedNameField: "form.guardianCertificate-name",
      },
      "form.guardianCertificate-name": {
        mainField: "form.guardianCertificate",
        relatedField: "form.guardianInCharge",
        relatedNameField: "form.guardianInCharge-name",
      },
      "form.temporaryPowerJobInCharge-name": {
        mainField: "form.temporaryPowerJobInCharge",
        relatedField: "form.temporaryPowerJobCertificate",
        relatedNameField: "form.temporaryPowerJobCertificate-name",
      },
      "form.temporaryPowerJobCertificate-name": {
        mainField: "form.temporaryPowerJobCertificate",
        relatedField: "form.temporaryPowerJobInCharge",
        relatedNameField: "form.temporaryPowerJobInCharge-name",
      },
    };

    const fields = fieldMap[field];
    if (fields) {
      const newValue = remove(idx, 1, formApi.getValue(fields.mainField));
      const relatedValue = remove(
        idx,
        1,
        formApi.getValue(fields.relatedField)
      );
      const relatedNameValue = remove(
        idx,
        1,
        formApi.getValue(fields.relatedNameField)
      );

      formApi.setValue(fields.mainField, newValue);
      formApi.setValue(fields.relatedField, relatedValue);
      formApi.setValue(fields.relatedNameField, relatedNameValue);
    }
  };

  const renderHelpText = () => {
    if (!isHighWork) return null;

    // 获取当前表单字段值
    const level = formApi.getValue("form.level");
    const isUpgrade = formApi.getValue("form.isUpgrade");

    // 显示前置条件提示
    if (!level) {
      return (
        <div className="text-xs text-orange-500">请先选择高处作业级别</div>
      );
    }

    /* if (!isUpgrade) {
      return <div className="text-xs text-orange-500">请先选择是否升级</div>;
    } */

    // 获取规则
    const ruleItem = rule.find((o) => o.highLevel === level);
    if (!ruleItem?.rangeRuleList?.length) {
      return (
        <div className="text-xs text-gray-500">当前级别暂无高度限制规则</div>
      );
    }

    // 生成规则提示文本，如果 isUpgrade === 2，过滤掉下限规则
    const hints = ruleItem.rangeRuleList
      .filter(({ operator }) => {
        // 如果 isUpgrade === 2，只显示上限规则
        if (isUpgrade === 2 && (operator === 2 || operator === 4)) {
          return false;
        }
        return true;
      })
      .map(({ operator, pivotNumber }) => {
        switch (operator) {
          case 1:
            return `必须小于 ${pivotNumber}米`;
          case 2:
            return `必须大于 ${pivotNumber}米`;
          case 3:
            return `必须小于等于 ${pivotNumber}米`;
          case 4:
            return `必须大于等于 ${pivotNumber}米`;
          case 5:
            return `必须等于 ${pivotNumber}米`;
          default:
            return "";
        }
      })
      .filter(Boolean);

    if (hints.length === 0) {
      // 当过滤后没有规则时，说明升级作业已跳过所有限制
      if (isUpgrade === 2) {
        return (
          <div className="text-xs text-gray-500">
            升级作业：已跳过所有高度限制
          </div>
        );
      }
      return (
        <div className="text-xs text-gray-500">
          当前级别暂无可用的高度限制规则
        </div>
      );
    }

    return <div className="text-xs text-gray-500">{hints.join("，")}</div>;
  };

  switch (item?.compType ?? "") {
    case "plainText":
      if (!formApi.getValue(field)) {
        formApi.setValue(field, item?.formData?.actualValue ?? "");
      }
      return (
        <div
          className={[
            "text-sm font-semibold flex items-center",
            item.formData?.textType == "title"
              ? "semi-form-section-text col-span-3"
              : "",
          ].join(" ")}
          style={{ justifyContent: item.formData?.textAlign ?? "left" }}
        >
          {item?.formData?.actualValue}
        </div>
      );
    case "table":
      return <RenderTable item={item} />;
    case "input":
      return isInputNumber ? (
        <>
          <Form.InputNumber
            key={`${k}-${formApi.getValue("form.level")}-${formApi.getValue("form.isUpgrade")}`}
            field={field}
            field-key={field}
            className="w-full"
            initValue={item?.formData?.defaultText}
            label={name}
            noLabel={isTable}
            precision={item?.formData?.type == "int" ? 0 : 2}
            placeholder={item?.formData?.placeHolder}
            helpText={renderHelpText()}
            validate={asyncValidate}
            rules={[
              {
                required: Boolean(item?.formData?.isReq === "required"),
                message: `${name}为必填项`,
              },
            ]}
          />
        </>
      ) : (
        <Form.Input
          key={k}
          field={field}
          field-key={field}
          className="w-full"
          initValue={item?.formData?.defaultText}
          label={name}
          noLabel={isTable}
          placeholder={item?.formData?.placeHolder}
          rules={[
            {
              required: Boolean(item?.formData?.isReq === "required"),
              message: `${name}为必填项`,
            },
          ]}
        />
      );
    case "radio":
      return (
        <Form.RadioGroup
          key={k}
          field={field}
          field-key={field}
          label={name}
          noLabel={isTable}
          rules={[
            {
              required: Boolean(item?.formData?.isReq === "required"),
              message: `${name}为必填项`,
            },
          ]}
        >
          {(item?.formData?.candidateList ?? []).map(
            (o: any, index: number) => (
              <Form.Radio value={o?.id ?? index} key={index}>
                {o.label}
              </Form.Radio>
            )
          )}
        </Form.RadioGroup>
      );
    case "checkbox":
      return (
        <Form.CheckboxGroup
          key={k}
          field={field}
          field-key={field}
          label={name}
          noLabel={isTable}
          className="w-full"
          rules={[
            {
              required: Boolean(item?.formData?.isReq === "required"),
              message: `${name}为必填项`,
            },
          ]}
        >
          {(item?.formData?.candidateList ?? []).map(
            (o: any, index: number) => (
              <Form.Checkbox value={o.option} key={index}>
                {o.option}
              </Form.Checkbox>
            )
          )}
        </Form.CheckboxGroup>
      );

    case "selector":
      if (item?.business === "department") {
        return (
          <DepartmentPicker
            key={k}
            field={field}
            label={name}
            field-key={field}
            noLabel={isTable}
            placeholder={item?.formData?.placeHolder ?? "请选择部门"}
            multiple={item?.formData?.multiple ?? false}
            valueType="object"
            rules={[
              {
                required: Boolean(item?.formData?.isReq === "required"),
                message: `${name}为必填项`,
              },
            ]}
          />
        );
      }
      if (item?.business === "workArea") {
        return (
          <AreaSearch
            key={k}
            field={field}
            field-key={field}
            label={name}
            placeholder={`请选择${name}`}
            valueType="object"
            multiple
            max={item?.formData?.multiple ? 99 : 1}
            isRequired
          />
        );
      }
      if (
        formApi.getValue("form.unitCategory") === 1 &&
        item.business === "unit"
      ) {
        return (
          <DepartmentPicker
            key={k}
            field={field}
            label={name}
            field-key={field}
            placeholder={item?.formData?.placeHolder ?? "请选择部门"}
            isRequired={Boolean(item?.formData?.isReq === "required")}
            valueType="object"
          />
        );
      }
      if (
        formApi.getValue("form.unitCategory") === 2 &&
        item.business === "unit"
      ) {
        return (
          <ContractorSearch
            key={k}
            field={field}
            label={name}
            filter={{ isBlack: 2 }}
            field-key={field}
            placeholder={item?.formData?.placeHolder ?? "请选择承包商"}
            isRequired={Boolean(item?.formData?.isReq === "required")}
            valueType="object"
          />
        );
      }

      // 作业监护人 || 临时用电作业负责人
      if (
        item.business === "guardianInCharge" ||
        item.business === "guardianCertificate" ||
        item.business === "temporaryPowerJobInCharge" ||
        item.business === "temporaryPowerJobInChargeCertificate"
      ) {
        return (
          <Form.TagInput
            key={k}
            field={`${field}-name`}
            className="w-full"
            field-key={field}
            label={`${name}`}
            placeholder={item?.formData?.placeHolder}
            onRemove={(removeValue, idx) =>
              handleRemove(`${field}-name`, idx, formApi)
            }
            onFocus={() => {
              setAtom({
                visible: true,
                type:
                  item.business === "guardianInCharge" ||
                  item.business === "guardianCertificate"
                    ? "guardian"
                    : "temporary",
              });
            }}
            rules={[
              {
                required: Boolean(item?.formData?.isReq === "required"),
                message: `${name}为必填项`,
              },
            ]}
          />
        );
      }

      return (
        <Form.Select
          key={k}
          field={field}
          field-key={field}
          label={name}
          noLabel={isTable}
          placeholder={item?.formData?.placeHolder}
          multiple={item?.formData?.multiple ?? false}
          className="w-full"
          rules={[
            {
              required: Boolean(item?.formData?.isReq === "required"),
              message: `${name}为必填项`,
            },
          ]}
        >
          {(item?.formData?.candidateList ?? []).map(
            (o: any, index: number) => (
              <Form.Select.Option value={o.id} key={index}>
                {o.label}
              </Form.Select.Option>
            )
          )}
        </Form.Select>
      );
    case "datePicker":
      const presets = [
        {
          text: "今天",
          start: new Date(),
          end: new Date(),
        },
        {
          text: "明天",
          start: new Date(new Date().valueOf() + 1000 * 3600 * 24),
          end: new Date(new Date().valueOf() + 1000 * 3600 * 24),
        },
      ];
      return (
        <Form.DatePicker
          key={k}
          field={field}
          field-key={field}
          label={name}
          placeholder={item?.formData?.placeHolder}
          className="w-full"
          type={item?.formData?.dateType}
          presets={presets}
          presetPosition="bottom"
          rules={[
            {
              required: Boolean(item?.formData?.isReq === "required"),
              message: `${name}为必填项`,
            },
          ]}
        />
      );

    case "employeePicker":
      // 满足条件并且不限制人员
      if (
        (item?.formData?.serviceRange?.includes?.(2) ||
          item?.formData?.serviceRange?.includes?.(3)) &&
        !item?.formData?.candidateList?.length
      ) {
        return (
          <div>
            <EmployeePicker
              callback={(list: any) => {
                const newList = list?.map((o: any) => {
                  let obj = {
                    ...o,
                    type: o._type === 1 ? 1 : o._type === 2 ? 1 : 2, // 1和2代表内部人员(1)；3代表外部人员(2)
                  };
                  return obj;
                });
                formApi.setValue(field, newList);
                const names: string[] = [];
                newList?.forEach((o: any) => {
                  names.push(o?.name);
                });
                formApi.setValue(`${field}-renderToText`, names.toString());
              }}
              showOptions={false}
              showBtn={false}
              options={formApi.getValue(field) ?? []}
              show={visible}
              setShow={setVisible}
              serviceRange={item.formData?.serviceRange ?? []}
            />
            <Form.Input
              onClick={() => {
                setVisible(true);
              }}
              key={k}
              field={`${field}-renderToText`}
              className="w-full"
              label={name}
              placeholder={item?.formData?.placeHolder ?? `请选择${name}`}
            />
          </div>
        );
      }
      // 满足条件但是限制人员-渲染为select
      if (item?.formData?.candidateList?.length) {
        return (
          <Form.Select
            key={k}
            field={field}
            field-key={field}
            label={name}
            multiple
            max={item?.formData?.multiple ? 99 : 1}
            className="w-full"
            placeholder={item?.formData?.placeHolder}
            clearIcon
            rules={[
              {
                required: Boolean(item?.formData?.isReq === "required"),
                message: `${name}为必填项`,
              },
            ]}
          >
            {(item?.formData?.candidateList ?? []).map(
              (o: any, index: number) => (
                <Form.Select.Option
                  value={
                    JSON.stringify(pick(["id", "name", "type"], o)) ?? index
                  }
                  key={index}
                >
                  {o.name}
                </Form.Select.Option>
              )
            )}
          </Form.Select>
        );
      }
      return (
        <EmployeeSearch
          key={k}
          field={field}
          label={name}
          field-key={field}
          placeholder={item?.formData?.placeHolder}
          isRequired={Boolean(item?.formData?.isReq === "required")}
          pickKeys={item?.formData?.candidateList ?? []}
          multiple
          max={item?.formData?.multiple ? 99 : 1}
          valueType="object"
        />
      );

    case "riskPicker":
      return (
        <Form.Select
          key={k}
          field={field}
          field-key={field}
          className="w-full"
          label={name}
          filter
          multiple
          maxTagCount={3}
          showRestTagsPopover={true}
          placeholder={item?.formData?.placeHolder ?? `请选择${name}`}
          restTagsPopoverProps={{ position: "top" }}
        >
          {riskMeasureOptions.map((i) => {
            const item = find(propEq(i.accidentType, "id"))(
              RISK_MEASURE_ACCIDENTTYPE
            );
            return (
              <Form.Select.Option value={i.id}>
                <Tooltip content={`${item?.name}: ${i?.safetyMeasure}`}>
                  {item?.name}
                </Tooltip>
              </Form.Select.Option>
            );
          })}
        </Form.Select>
      );

    case "mapPicker":
      // 如果厂家有3D模型就调用模型， 没有就调用百度地图
      return (
        <div>
          <MapPicker field={field} />

          <Form.Input
            key={k}
            onClick={() => {
              setMapPicker({ visible: true });
            }}
            field={field}
            field-key={field}
            label={name}
            placeholder={`请设置${name}`}
            rules={[{ required: true, message: `${name}为必填项` }]}
          />
        </div>
      );
    case "annexImgPicker":
      return (
        <Upload
          field={field}
          formField={field}
          field-key={field}
          //listType='list'
          label={name}
          rules={[
            {
              required: Boolean(item?.formData?.isReq === "required"),
              message: `${name}为必填项`,
            },
          ]}
          multiple
        />
      );
    case "annexFilePicker":
      return (
        <Upload
          type="file"
          listType="list"
          field={field}
          formField={field}
          field-key={field}
          label={name}
          rules={[
            {
              required: Boolean(item?.formData?.isReq === "required"),
              message: `${name}为必填项`,
            },
          ]}
          multiple
        />
      );
    default:
      return <div>未定义</div>;
  }
};
