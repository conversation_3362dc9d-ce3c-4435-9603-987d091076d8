import { Form, Table } from "@douyinfe/semi-ui";
import {
  certificateColumnsAtom,
  certificatePickerAtom,
  certificatePickerDataAtom,
} from "atoms";
import { CertificateTableModal } from "components";
import { useAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { useEffect, useMemo } from "react";

interface JobWorkersTableProps {
  required?: boolean;
}

export const JobWorkersTable = ({ required = false }: JobWorkersTableProps) => {
  const [, setAtom] = useAtom(certificatePickerAtom);
  const reset = useResetAtom(certificatePickerDataAtom);
  const [data] = useAtom(certificatePickerDataAtom);
  const [_columns] = useAtom(certificateColumnsAtom);

  useEffect(() => {
    return () => {
      reset();
    };
  }, []);

  const columns = useMemo(() => {
    return _columns.filter((o: any) => {
      return o.dataIndex != "issueDate" && o.dataIndex != "expireDate";
    });
  }, [_columns]);

  const handleOpen = () => {
    setAtom(true);
  };

  return (
    <div className="flex flex-col gap-4 mt-8 relative">
      <CertificateTableModal />
      <Form.Section
        text={
          <>
            持证作业人员
            {required && <span className="text-red-500 ml-1">*</span>}
          </>
        }
      >
        <div className="absolute right-0 top-[-10px]">
          <span className="btn rounded btn-primary btn-sm" onClick={handleOpen}>
            {data?.record?.length ? "编辑" : "新增"}
          </span>
        </div>
        <Table
          className="rounded overflow-hidden mt-4"
          //rowKey="id"
          columns={columns}
          dataSource={data?.record ?? []}
          onHeaderRow={(columns, index) => {
            return {
              className: "text-gray-900 text-opacity-90 bg-gray-50",
            };
          }}
          headerStyle={{ color: "blue" }}
          pagination={false}
        />
      </Form.Section>
    </div>
  );
};
