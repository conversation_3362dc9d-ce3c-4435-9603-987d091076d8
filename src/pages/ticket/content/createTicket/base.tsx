import { Col, Form, Row, useFormApi } from "@douyinfe/semi-ui";
import { useAuth } from "@reactivers/hooks";
import { useQuery } from "@tanstack/react-query";
import { getEmployee, getJobSliceCode } from "api";
import { jobAppointmentFilterAtom } from "atoms";
import {
  DepartmentSearch,
  EmployeeSearch,
  JobAppointmentTableModal,
} from "components";
import { useAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

const ComponentUsingFormApi = ({ tmpl }) => {
  const { user } = useAuth();
  const params = useParams<{ id: string; cid: string }>();
  const [visible, setVisible] = useState(false);
  const formApi = useFormApi();
  const [jobAppointmentFilter, setSafetyAnalysisFilter] = useAtom(
    jobAppointmentFilterAtom,
  );
  const reset = useResetAtom(jobAppointmentFilterAtom);
  const { data } = useQuery({
    queryKey: ["getEmployee", user?.userInfo?.id],
    queryFn: () => {
      return getEmployee(user?.userInfo?.id);
    },
  });
  const { data: code } = useQuery({
    queryKey: ["getJobSliceCode", params?.cid],
    queryFn: () => {
      return getJobSliceCode(params?.cid);
    },
    enabled: Boolean(params?.cid),
  });

  const rules = [
    {
      required: Boolean(tmpl?.allowNoAppointment === 2),
      message: "作业预约号不可为空",
    },
  ];
  const handleVisible = () => {
    // 打开预约号选择之前，必须筛选:  审核通过且不过期
    let filter = {
      status: 2,
      isOvertime: 2,
      categoryIdsJsonIn: parseInt(params?.cid ?? "0"),
    };
    if (!params?.cid) {
      delete filter.categoryIdsJsonIn;
    }
    setSafetyAnalysisFilter({
      ...jobAppointmentFilter,
      filter: filter,
    });
    setVisible(true);
  };

  useEffect(() => {
    if (data?.data?.department?.id) {
      formApi.setValue("applyDepartmentId", data?.data?.department?.id);
    }
    if (code?.data?.code) {
      formApi.setValue("code", code?.data?.code);
    }
    formApi.setValue("applyPersonId", user?.userInfo?.id);
    formApi.setValue("formTemplateId", tmpl?.formTemplateId);
    formApi.setValue("processTemplateId", tmpl?.processTemplateId);
    formApi.setValue("processes", tmpl?.processTemplate);
    formApi.setValue("templateId", tmpl?.id);
  }, [formApi, data, code]);

  const handleCallback = (selectedRecord: any) => {
    setVisible(false);
    reset();
    if (selectedRecord?.length) {
      const item = selectedRecord[0];
      formApi.setValue("appointmentId", item.id);
      formApi.setValue("appointmentId_code", item.code); // 显示用
      // formApi.setValue("planBeginTime", item.effectivetBeginTime);
      // formApi.setValue("planEndTime", item.effectivetEndTime);
      formApi.setValue("form.workContent", item?.content ?? "");
      if (item?.area?.id) {
        formApi.setValue("form.workArea", [JSON.stringify(item.area)]);
      }
    }
  };

  return (
    <>
      <JobAppointmentTableModal
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
        callback={handleCallback}
      />
      <Form.Input
        onClick={handleVisible}
        field="appointmentId_code"
        label="作业预约号"
        placeholder="点击选择预约号"
        rules={rules}
      />
    </>
  );
};

export const BaseTicket = ({ tmpl }) => {
  const presets = [
    {
      text: "今天",
      start: new Date(),
      end: new Date(),
    },
    {
      text: "明天",
      start: new Date(new Date().valueOf() + 1000 * 3600 * 24),
      end: new Date(new Date().valueOf() + 1000 * 3600 * 24),
    },
  ];
  const rules = [{ required: true, message: "该项不可为空" }];

  return (
    <div className="flex flex-col gap-4 ">
      <Form.Section text="基础信息">
        <Row gutter={16}>
          <Col span={8}>
            <ComponentUsingFormApi tmpl={tmpl} />
          </Col>
          <Col span={8}>
            <Form.Input field="name" label="作业票名称" rules={rules} />
          </Col>
          <Col span={8}>
            <Form.Input
              field="code"
              label="作业票编号"
              disabled
              rules={rules}
            />
          </Col>
          <Col span={8}>
            <EmployeeSearch field="applyPersonId" label="申请人" isRequired />
          </Col>
          <Col span={8}>
            <DepartmentSearch
              field="applyDepartmentId"
              label="申请单位"
              isRequired
            />
          </Col>
          <Col span={8}>
            <Form.DatePicker
              field="planBeginTime"
              className="w-full"
              label="计划开始时间"
              rules={rules}
              type="dateTime"
              presets={presets}
              presetPosition="bottom"
            />
          </Col>
          <Col span={8}>
            <Form.DatePicker
              field="planEndTime"
              className="w-full"
              label="计划结束时间"
              rules={rules}
              type="dateTime"
              presets={presets}
              presetPosition="bottom"
            />
          </Col>
        </Row>
      </Form.Section>
    </div>
  );
};
