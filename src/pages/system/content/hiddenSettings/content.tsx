import {
  Col,
  Form,
  Row,
  Text<PERSON>rea,
  Toast,
  useForm<PERSON>pi,
  useFormState,
} from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { getHiddenSettings, setHiddenSettings } from "api/system/hidden";
import { platformConfigAtom } from "atoms";
import {
  IS_ISNOT_MAP,
  LOCATION_SERVICE_PROVIDER,
  SPECIAL_CLIENT_MAP,
  SYSTEM_WEB_PROTOCOL_MAP,
} from "components";
import copy from "copy-to-clipboard";
import { useAtom } from "jotai";
import { omit } from "ramda";
import { useEffect, useRef } from "react";
import { filterEditData } from "utils";

const FormDebugComponentUsingFormState = () => {
  const formState = useFormState();
  return (
    <>
      <TextArea rows={8} value={JSON.stringify(formState.values, null, 2)} />
    </>
  );
};

const LocationServiceComponentUsingFormApi = () => {
  const formApi = useFormApi();
  const formState = useFormState();

  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  return formState.values.locationServiceType !== 10 ? (
    <>
      <Row gutter={gutter}>
        <Col span={24}>
          <Form.Input
            label="免密登录URL"
            field="locationUrl"
            trigger="blur"
            rules={rules}
          />
        </Col>
        <Col span={24}>
          <Form.Input
            label="人员轨迹URL"
            field="locationPathUrl"
            trigger="blur"
          />
        </Col>
        <Col span={24}>
          <Form.Input
            label="API服务URL"
            field="locationApiUrl"
            trigger="blur"
          />
        </Col>
      </Row>
      <Row gutter={gutter}>
        <Col span={12}>
          <Form.Input
            label="用户名"
            field="locationApiUsername"
            trigger="blur"
          />
        </Col>
        <Col span={12}>
          <Form.Input label="密码" field="locationApiPassword" trigger="blur" />
        </Col>
      </Row>
    </>
  ) : null;
};

const RuidaLOcationServiceComponentUsingFormApi = () => {
  const formApi = useFormApi();
  const formState = useFormState();

  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  return formState.values.locationServiceType === 1 ? (
    <>
      <Row gutter={gutter}>
        <Col span={12}>
          <Form.RadioGroup
            label="人员聚集开关是否打开"
            field="locationPaIsOn"
            rules={[{ required: true, message: "此为必填项" }]}
          >
            {IS_ISNOT_MAP.map((item) => (
              <Form.Radio key={item.id} value={item.id}>
                {item.name}
              </Form.Radio>
            ))}
          </Form.RadioGroup>
        </Col>
      </Row>
    </>
  ) : null;
};

const ZhenyuanLocationServiceComponentUsingFormApi = () => {
  const formApi = useFormApi();
  const formState = useFormState();

  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  return formState.values.locationServiceType === 2 ? (
    <>
      <Row gutter={gutter}>
        <Col span={12}>
          <Form.Input
            label="真源服务商密码类型"
            field="locationGrantType"
            trigger="blur"
            rules={rules}
          />
        </Col>
        <Col span={12}>
          <Form.Input
            label="真源免密用户名"
            field="locationMmUsername"
            trigger="blur"
            rules={rules}
          />
        </Col>
      </Row>
      <Row gutter={gutter}>
        <Col span={12}>
          <Form.Input
            label="真源免密租户id"
            field="locationMmTenantId"
            trigger="blur"
            rules={rules}
          />
        </Col>
        <Col span={12}>
          <Form.Input
            label="真源免密密码"
            field="locationMmPassword"
            trigger="blur"
            rules={rules}
          />
        </Col>
      </Row>
    </>
  ) : null;
};

export const HiddenSettingsContent = ({ readonly = false, ...restProps }) => {
  const gutter = 24;
  const rules = [{ required: true, message: "此为必填项" }];

  const queryClient = useQueryClient();
  const queryKey = ["getHiddenSettings"];

  const formRef = useRef<any>(null);

  const [platformConfig, setPlatformConfig] = useAtom(platformConfigAtom);

  const { isLoading, data } = useQuery({
    queryKey: [queryKey],
    queryFn: () => {
      return getHiddenSettings();
    },
  });

  useEffect(() => {
    if (data && formRef.current?.formApi) {
      const items = omit([], data?.data);
      // console.log(items, 'itemsitemsitems');

      formRef.current?.formApi.setValues(
        {
          ...filterEditData(items),
        },
        { isOverride: true }
      );
    } else {
      formRef.current?.frormApi?.reset?.();
    }
  }, [data, formRef.current?.formApi]);

  const hiddenSettingsMutation = useMutation({
    mutationFn: setHiddenSettings,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
      }
    },
  });

  const handleSubmit = (values) => {
    hiddenSettingsMutation.mutate(formRef.current?.formApi.getValues());
  };

  const handleCopyUrl = () => {
    const uid = data.data?.pfUid;
    const pwd = data.data?.pfPassword;
    const url = encodeURIComponent("/");
    const text = `${window.location.origin}/auth?uid=${uid}&password=${pwd}&redirect=${url}`;
    copy(text);
    Toast.success("复制成功");
  };

  return (
    <div className="bg-white shadow p-4 h-fit rounded">
      <Form
        labelPosition="left"
        labelAlign="right"
        labelWidth={160}
        ref={formRef}
        onSubmit={(values) => handleSubmit(values)}
        autoScrollToError
      >
        {/* add form items here */}
        {({ formState }) => (
          <>
            {/* <FormDebugComponentUsingFormState /> */}
            <Form.Section text="前端网站访问地址配置">
              <Row gutter={gutter}>
                <Col span={4}>
                  <Form.Select
                    noLabel
                    placeholder="协议类型"
                    field="wsSchemaType"
                    className="w-full"
                    rules={rules}
                  >
                    {SYSTEM_WEB_PROTOCOL_MAP.map((item) => (
                      <Form.Select.Option key={item.id} value={item.id}>
                        {item.name}
                      </Form.Select.Option>
                    ))}
                  </Form.Select>
                </Col>
                <Col span={6}>
                  <Form.Input
                    noLabel
                    placeholder="公网主机名(Host)"
                    field="wsHost"
                    trigger="blur"
                    rules={rules}
                  />
                </Col>
                <Col span={4}>
                  <Form.InputNumber
                    noLabel
                    placeholder="公网端口号(Port)"
                    field="wsPort"
                    trigger="blur"
                    prefix=":"
                    rules={rules}
                  />
                </Col>
              </Row>
            </Form.Section>

            <Form.Section text="视频服务访问地址配置">
              <Row gutter={gutter}>
                <Col span={4}>
                  {/* <Label>服务地址</Label> */}
                  <Form.Select
                    noLabel
                    placeholder="协议类型"
                    field="vsSchemaType"
                    className="w-full"
                    rules={rules}
                  >
                    {SYSTEM_WEB_PROTOCOL_MAP.map((item) => (
                      <Form.Select.Option key={item.id} value={item.id}>
                        {item.name}
                      </Form.Select.Option>
                    ))}
                  </Form.Select>
                </Col>
                <Col span={6}>
                  <Form.Input
                    noLabel
                    placeholder="公网主机名(Host)"
                    field="vsHost"
                    trigger="blur"
                    rules={rules}
                  />
                </Col>
                <Col span={4}>
                  <Form.InputNumber
                    noLabel
                    placeholder="公网端口号(Port)"
                    field="vsPort"
                    trigger="blur"
                    prefix=":"
                    rules={rules}
                  />
                </Col>
              </Row>
              <Row gutter={gutter}>
                {/* <Col span={7}>
                  <Form.Input label="内网主机名(一般不需要修改)" labelWidth={210} field="vsInnerHost" trigger='blur' rules={rules} />
                </Col> */}
                {/* <Col span={12}>
                  <Form.Input label="用户名密码" field="vsUserPassword" trigger='blur' disabled rules={rules} />
                </Col> */}
              </Row>
            </Form.Section>

            <Form.Section text="免密链接配置">
              <Row gutter={gutter}>
                <Col span={8}>
                  <Form.InputNumber label="UID" field="pfUid" trigger="blur" />
                </Col>
                <Col span={8}>
                  <Form.Input label="密码" field="pfPassword" trigger="blur" />
                </Col>
                <Col span={8}>
                  <button
                    type="button"
                    className="btn btn-primary btn-sm rounded w-[150px] my-3 mx-4"
                    onClick={handleCopyUrl}
                  >
                    生成链接
                  </button>
                </Col>
              </Row>
              <Row gutter={gutter}>
                {/* <Col span={12}>
                  <Form.Input label="请求验证Key(一般不用改)" field="pfHeaderKey" trigger='blur' rules={rules} />
                </Col> */}
                {/* <Col span={12}>
                  <Form.Input label="确认码(相当于固定密码)" field="pfHeaderValue" trigger='blur' rules={rules} />
                </Col> */}
              </Row>
            </Form.Section>

            <Form.Section text="其它系统设置">
              <Row gutter={gutter}>
                <Col span={12}>
                  <Form.InputNumber
                    label="APP最低版本要求"
                    field="appMinVersion"
                    trigger="blur"
                  />
                </Col>
                <Col span={12}>
                  <Form.Select
                    label="特殊客户"
                    field="specialClient"
                    className="w-full"
                    rules={rules}
                  >
                    {SPECIAL_CLIENT_MAP.map((item) => (
                      <Form.Select.Option key={item.id} value={item.id}>
                        {item.name}
                      </Form.Select.Option>
                    ))}
                  </Form.Select>
                </Col>
              </Row>
              <Row gutter={gutter}>
                <Col span={24}>
                  <Form.Input
                    label="系统授权码"
                    field="systemAuthCode"
                    trigger="blur"
                    // rules={rules}
                  />
                </Col>
              </Row>
            </Form.Section>

            <Form.Section text="人员定位服务配置">
              <Row gutter={gutter}>
                <Col span={6}>
                  <Form.Select
                    label="人员定位服务类型"
                    field="locationServiceType"
                    className="w-full"
                    rules={rules}
                  >
                    {LOCATION_SERVICE_PROVIDER.map((item) => (
                      <Form.Select.Option key={item.id} value={item.id}>
                        {item.name}
                      </Form.Select.Option>
                    ))}
                  </Form.Select>
                </Col>
              </Row>
              <LocationServiceComponentUsingFormApi />
              <RuidaLOcationServiceComponentUsingFormApi />
              <ZhenyuanLocationServiceComponentUsingFormApi />
            </Form.Section>

            {/* TODO: draft支持非Modal的Form */}
            {/* {editModalAtom?.id ? null : <Draft id={uniqueKey} draftAtom={atoms.editModal} />} */}
            <div className="flex gap-2 justify-end">
              <button className="btn btn-primary btn-sm rounded w-[150px]">
                提交
              </button>
              {/* <Button type="primary" htmlType="submit" className="btn btn-primary btn-sm rounded">提交</Button> */}
              {/* <Button htmlType="reset">重置(reset)</Button> */}
            </div>
            {/*             <Button type="primary" htmlType="submit" className="btn-margin-right">提交(submit)</Button>
            <Button htmlType="reset">重置(reset)</Button> */}
          </>
        )}
      </Form>
    </div>
  );
};
