import { useQuery } from "@tanstack/react-query";
import { postJobCategoryJobSlice } from "api";
import { useHiddenSettings } from "hooks";
import { Echart } from "pages/bigScreen/component/Chart";
import { sum } from "ramda";
import { useCallback, useMemo, useState } from "react";

export const Left = ({ readonly = false, ...restProps }) => {
  const [type, setType] = useState<1 | 2 | 3 | 4 | 5>(1);
  const [isHighRisk, setIsHighRisk] = useState(false);
  const queryFn = useCallback(() => {
    const params: any = { type };
    if (isHighRisk) {
      params.isHighRisk = 1;
    }
    return postJobCategoryJobSlice(params);
  }, [type, isHighRisk]);

  const { data, isLoading } = useQuery({
    queryKey: ["postJobCategoryJobSlice", type, isHighRisk],
    queryFn,
  });

  const hiddenSettings = useHiddenSettings();

  const dataSource = useMemo(() => {
    return data?.data;
  }, [data]);

  const handleActiveTab = (val: number) => {
    if (!isLoading) {
      setType(val);
    }
  };

  const handleHighRiskChange = (checked: boolean) => {
    if (!isLoading) {
      setIsHighRisk(checked);
    }
  };

  const option = useMemo(() => {
    if (dataSource?.jobCategoryStats?.length) {
      const list = [];
      const total = [];
      dataSource.jobCategoryStats.forEach((o) => {
        total.push(o?.count);
        list.push({
          value: o?.count ?? 0,
          name: `${o.name}  ${o?.count}`,
        });
      });

      return {
        title: {
          show: false,
          text: ``,
          left: "30%",
          bottom: "0%",
        },

        tooltip: {
          trigger: "item",
        },
        legend: {
          orient: "vertical",
          left: "65%",
          top: "center",
        },

        series: [
          {
            center: ["30%", "50%"],
            name: "",
            type: "pie",
            radius: ["35%", "60%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: "#fff",
              borderWidth: 2,
            },
            label: {
              show: false,
              formatter(params) {
                return `{name|${params.name}}\n{percent|${params.value + "个"}}`;
              },
              rich: {
                name: {
                  // color: "#ccc",
                  fontSize: 12,
                  padding: [0, 0],
                  align: "center",
                },
                percent: {
                  align: "center",
                  fontSize: 14,
                  padding: [0, 0],
                },
              },
              color: "#FFF",
              opacity: 1,
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: list,
          },
        ],
      };
    }
    return {};
  }, [dataSource]);

  const totalCount = useMemo(() => {
    if (dataSource?.jobCategoryStats?.length) {
      const list = [];
      const total = [0];
      dataSource.jobCategoryStats.forEach((o) => {
        total.push(o?.count ?? 0);
      });
      return sum(total);
    }
    return 0;
  }, [dataSource]);

  return (
    <div className="flex flex-col w-full gap-y-5">
      <div className="flex flex-col bg-white border border-[#EDEDEE] rounded-lg p-6 divide-y gap-y-5">
        <div className="flex justify-between">
          <div className="flex items-center gap-x-[10px]">
            <div className="bg-[#60B7FF] w-1 h-[22px]"></div>
            <span className="text-[18px] font-bold">作业票数量统计</span>
          </div>

          <div className="flex gap-2 items-center">
            {hiddenSettings?.specialClient === 2 && (
              <div className="border border-[#60B7FF] text-[#60B7FF] text-sm rounded-md px-3 py-2">
                <label className="flex items-center gap-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={isHighRisk}
                    onChange={(e) => handleHighRiskChange(e.target.checked)}
                    disabled={isLoading}
                    className="w-4 h-4 text-[#60B7FF] border-[#60B7FF] rounded focus:ring-[#60B7FF] focus:ring-2 accent-[#60B7FF]"
                  />
                  <span className="font-medium">只看高风险</span>
                </label>
              </div>
            )}
            <div className="border border-[#60B7FF] text-[#60B7FF] text-sm cursor-pointer overflow-hidden rounded-md flex">
              <div
                className={`leading-none px-[14px] border-r border-[#60B7FF] py-[8px] ${type === 1 ? "text-white bg-[#60B7FF]" : ""}`}
                onClick={() => {
                  handleActiveTab(1);
                }}
              >
                今日
              </div>
              <div
                className={`leading-none px-[14px] border-r border-[#60B7FF] py-[8px] ${type === 2 ? "text-white bg-[#60B7FF]" : ""}`}
                onClick={() => {
                  handleActiveTab(2);
                }}
              >
                本周
              </div>
              <div
                className={`leading-none px-[14px] border-r border-[#60B7FF] py-[8px] ${type === 3 ? "text-white bg-[#60B7FF]" : ""}`}
                onClick={() => {
                  handleActiveTab(3);
                }}
              >
                本月
              </div>
              <div
                className={`leading-none px-[14px] border-r border-[#60B7FF] py-[8px] ${type === 4 ? "text-white bg-[#60B7FF]" : ""}`}
                onClick={() => {
                  handleActiveTab(4);
                }}
              >
                本年
              </div>
              <div
                className={`leading-none px-[14px] py-[8px] ${type === 5 ? "text-white bg-[#60B7FF]" : ""}`}
                onClick={() => {
                  handleActiveTab(5);
                }}
              >
                全部
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col w-full gap-y-6 ">
          <div className="w-full h-[500px] relative">
            <Echart option={option} />
            <div className="flex flex-col items-center justify-center absolute top-[210px] left-[27%]">
              <span className="text-lg font-extrabold text-[#666]">合计</span>
              <span className="text-[34px] font-extrabold text-[#333]">
                {totalCount}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
