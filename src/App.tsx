/* eslint-disable @typescript-eslint/no-type-alias */
import { Spin } from "@douyinfe/semi-ui";
import { Auth } from "auth";
import { redirectToDraftIfNeeded } from "components/Draft";
import BigScreen from "pages/bigScreen/template";
import { NotFound } from "pages/notFound";

import { useFavicon, useTitle } from "ahooks";
import { LayoutBox } from "pages/layout";
import {
  AuthDingTalkPage,
  AuthPage,
  LandrayOASSOCallback,
  LoginPage,
} from "pages/login";
import { PrintPreviewPage } from "pages/ticket";
import { useEffect, useMemo, type ReactElement } from "react";
import { RouterProvider, createBrowserRouter } from "react-router-dom";
// import '@semi-bot/semi-theme-vrdemo/semi.min.css';
import { useQuery } from "@tanstack/react-query";
import { getPlatformConfig, lastPath } from "api";
import { platformConfigAtom } from "atoms";
import { base_url } from "config";
import { useAtom } from "jotai";
import { MapPage } from "pages/map";
import { VideoPage } from "pages/video";
import "remixicon/fonts/remixicon.css";
import { RoleRouter } from "routes";
import "tdesign-react/es/style/index.css";
// import { TrainingRecordPage } from 'pages/training/recordPage';

import { PaperResultPage } from "pages/coporateTraining/resultPage";
import {
  AuthDingtalkRoute,
  BigScreenRoutes,
  H5MapRoute,
  LandrayOASSOCallbackRoute,
  LoginRoute,
  NotPermissionRoute,
  PaperResultRoute,
  PasswordFreeRoute,
  PrintPreviewRoute,
  VideoRoute,
} from "utils/routerConstants";

function Loading() {
  return (
    <div className="w-full h-screen flex items-center justify-center">
      <Spin size="large" />
    </div>
  );
}

/**
 * @file 异步加载组件
 */

// const SuspenseComponent = props => {
//   const {
//     Component, extraProps = {}, useLoading, importPath, importCompletesCallback, ...componentProps
//   } = props;

//   return <Suspense
//     fallback={<Loading />}>
//     <Component {...componentProps}
//       {...extraProps} />
//   </Suspense>;
// };

// const PendingComponent = (Component, extraProps = {}, useLoading, importPath) => props => {
//   return <SuspenseComponent
//     Component={Component}
//     extraProps={extraProps}
//     useLoading={useLoading}
//     importPath={importPath}
//     {...props} />;
// };

// function Pending(importFunction, extraProps = {}, useLoading = false) {
//   return PendingComponent(
//     React.lazy(() => import(importFunction)),
//     extraProps,
//     useLoading,
//     importFunction.toString(),
//   );
// }

// const lazy = (url: string) => {
//   const SettingsPage = React.lazy(() => import(url));
//   return <Suspense fallback={<Loading />}>
//     <SettingsPage />
//   </Suspense>
// }

// const BigScreen = Pending('./pages/bigScreen/template/index.tsx');

const createBaseRoter = () => [
  {
    // index: true,
    path: LoginRoute,
    element: <LoginPage />,
    name: "登录",
  },
  {
    path: PasswordFreeRoute,
    element: <AuthPage />,
    name: "免密登录",
  },
  {
    path: AuthDingtalkRoute,
    element: <AuthDingTalkPage />,
    name: "钉钉登陆",
  },
  {
    path: LandrayOASSOCallbackRoute,
    element: <LandrayOASSOCallback />,
    name: "蓝凌OA单点登录回调",
  },
  {
    path: `${PrintPreviewRoute}/:id`,
    element: <PrintPreviewPage />,
  },
  {
    path: BigScreenRoutes.HOME,
    element: <BigScreen />,
    name: "一张图",
  },
  {
    path: VideoRoute,
    element: <VideoPage />,
    name: "监控播放",
  },
  {
    path: `${PaperResultRoute}/:id`,
    element: <PaperResultPage />,
    name: "考试结果",
  },
  {
    path: `${H5MapRoute}/:type`,
    element: <MapPage />,
    name: "一张图",
  },
  {
    path: NotPermissionRoute,
    element: <NotFound statusCode={403} autoRedirect={false} />,
    name: "403",
  },
  {
    path: "/",
    element: <LayoutBox />,
    children: RoleRouter,
  },

  /* {
      index: true,
      path: "home",
      element: <DashBoard />,
    }, */
  {
    path: "*",
    element: <NotFound />,
  },
];

export default function App(): ReactElement {
  const [platformConfig, setPlatformConfig] = useAtom(platformConfigAtom);

  const { data: serverPlatformConfig } = useQuery({
    queryKey: ["getPlatformConfig"],
    queryFn: async () => getPlatformConfig(),
  });
  const serverPlatformConfigOptions = useMemo(
    () => serverPlatformConfig?.data ?? {},
    [serverPlatformConfig]
  );

  useEffect(() => {
    if (serverPlatformConfigOptions.platformTitle) {
      setPlatformConfig({
        ...serverPlatformConfigOptions,
        id: "custom",
        display_name: serverPlatformConfigOptions.platformTitle,
        logo_name: base_url + serverPlatformConfigOptions.icon,
        departmentRootName: serverPlatformConfigOptions.departmentRootName,
        webMenus: serverPlatformConfigOptions.webMenus,
        webUrlPrefix: serverPlatformConfigOptions.webUrlPrefix,
        cesiumUris: serverPlatformConfigOptions.cesiumUris,
      });
    }
  }, [serverPlatformConfigOptions]);

  useFavicon(platformConfig.logo_name);
  useTitle(platformConfig.display_name);
  /* useFavicon(getCurrentOem()?.logo_name);
  useTitle(getCurrentOem()?.display_name) */
  return (
    <Auth
      onLogin={(user) => {
        redirectToDraftIfNeeded(user.employee, () => {
          let replacePath = lastPath.get() ? lastPath.get() : "/";
          window.location.replace(replacePath);
        });
      }}
      defaultMenu={RoleRouter}
    >
      <RouterProvider router={createBrowserRouter(createBaseRoter())} />
    </Auth>
  );
}
