import { LandrayOASSOCallbackRoute } from "utils/routerConstants";
import { describe, expect, it } from "vitest";
import {
  buildCallbackURL,
  extractUUIDFromURL,
  validateUUID,
} from "../ssoUtils";

describe("ssoUtils", () => {
  describe("extractUUIDFromURL", () => {
    it("should extract UUID from valid callback URL", () => {
      const url = `https://example.com${LandrayOASSOCallbackRoute}?uuid=550e8400-e29b-41d4-a716-************`;
      const result = extractUUIDFromURL(url);
      expect(result).toBe("550e8400-e29b-41d4-a716-************");
    });

    it("should extract UUID from URL with multiple parameters", () => {
      const url = `https://example.com/callback?param1=value1&uuid=550e8400-e29b-41d4-a716-************&param2=value2`;
      const result = extractUUIDFromURL(url);
      expect(result).toBe("550e8400-e29b-41d4-a716-************");
    });

    it("should return null when UUID parameter is missing", () => {
      const url = `https://example.com${LandrayOASSOCallbackRoute}?other=param`;
      const result = extractUUIDFromURL(url);
      expect(result).toBeNull();
    });

    it("should return null when UUID parameter is empty", () => {
      const url = `https://example.com${LandrayOASSOCallbackRoute}?uuid=`;
      const result = extractUUIDFromURL(url);
      expect(result).toBeNull();
    });

    it("should return null for invalid URL format", () => {
      const invalidUrl = "not-a-valid-url";
      const result = extractUUIDFromURL(invalidUrl);
      expect(result).toBeNull();
    });

    it("should handle URL without query parameters", () => {
      const url = `https://example.com${LandrayOASSOCallbackRoute}`;
      const result = extractUUIDFromURL(url);
      expect(result).toBeNull();
    });

    it("should handle relative URLs with UUID", () => {
      const url = `${LandrayOASSOCallbackRoute}?uuid=550e8400-e29b-41d4-a716-************`;
      // This will fail with relative URLs, which is expected behavior
      const result = extractUUIDFromURL(url);
      expect(result).toBeNull();
    });
  });

  describe("validateUUID", () => {
    it("should validate correct UUID v4 format", () => {
      const validUUIDs = [
        "550e8400-e29b-41d4-a716-************",
        "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
        "6ba7b811-9dad-11d1-80b4-00c04fd430c8",
        "f47ac10b-58cc-4372-a567-0e02b2c3d479",
      ];

      validUUIDs.forEach((uuid) => {
        expect(validateUUID(uuid)).toBe(true);
      });
    });

    it("should validate UUID with uppercase letters", () => {
      const uuid = "550E8400-E29B-41D4-A716-************";
      expect(validateUUID(uuid)).toBe(true);
    });

    it("should reject invalid UUID formats", () => {
      const invalidUUIDs = [
        "550e8400-e29b-41d4-a716-44665544000", // too short
        "550e8400-e29b-41d4-a716-************0", // too long
        "550e8400-e29b-41d4-a716-44665544000g", // invalid character
        "550e8400e29b41d4a716************", // missing hyphens
        "550e8400-e29b-41d4-a716", // incomplete
        "not-a-uuid-at-all",
        "123",
        "",
      ];

      invalidUUIDs.forEach((uuid) => {
        expect(validateUUID(uuid)).toBe(false);
      });
    });

    it("should reject null and undefined values", () => {
      expect(validateUUID(null as any)).toBe(false);
      expect(validateUUID(undefined as any)).toBe(false);
    });

    it("should reject non-string values", () => {
      expect(validateUUID(123 as any)).toBe(false);
      expect(validateUUID({} as any)).toBe(false);
      expect(validateUUID([] as any)).toBe(false);
    });

    it("should reject empty string", () => {
      expect(validateUUID("")).toBe(false);
    });
  });

  describe("buildCallbackURL", () => {
    it("should build callback URL with base URL", () => {
      const baseUrl = "https://example.com";
      const result = buildCallbackURL(baseUrl);
      expect(result).toBe(`https://example.com${LandrayOASSOCallbackRoute}`);
    });

    it("should handle base URL with trailing slash", () => {
      const baseUrl = "https://example.com/";
      const result = buildCallbackURL(baseUrl);
      expect(result).toBe(`https://example.com${LandrayOASSOCallbackRoute}`);
    });

    it("should handle base URL with port", () => {
      const baseUrl = "https://example.com:8080";
      const result = buildCallbackURL(baseUrl);
      expect(result).toBe(
        `https://example.com:8080${LandrayOASSOCallbackRoute}`
      );
    });

    it("should handle base URL with path", () => {
      const baseUrl = "https://example.com/app";
      const result = buildCallbackURL(baseUrl);
      expect(result).toBe(
        `https://example.com/app${LandrayOASSOCallbackRoute}`
      );
    });

    it("should handle localhost URLs", () => {
      const baseUrl = "http://localhost:3000";
      const result = buildCallbackURL(baseUrl);
      expect(result).toBe(`http://localhost:3000${LandrayOASSOCallbackRoute}`);
    });
  });
});
