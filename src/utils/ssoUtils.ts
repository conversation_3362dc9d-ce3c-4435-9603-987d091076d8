/**
 * SSO utility functions for Landray OA integration
 */

import { LandrayOASSOCallbackRoute } from "./routerConstants";

/**
 * Extracts UUID from URL parameters
 * @param url - The URL to extract UUID from
 * @returns The UUID string if found, null otherwise
 */
export const extractUUIDFromURL = (url: string): string | null => {
  try {
    const urlObj = new URL(url);
    const uuid = urlObj.searchParams.get("uuid");

    if (!uuid) {
      return null;
    }

    return uuid;
  } catch (error) {
    // Invalid URL format
    return null;
  }
};

/**
 * Validates if a string is a valid UUID format
 * @param uuid - The UUID string to validate
 * @returns true if valid UUID format, false otherwise
 */
export const validateUUID = (uuid: string): boolean => {
  if (!uuid || typeof uuid !== "string") {
    return false;
  }

  // UUID v4 format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
  // where x is any hexadecimal digit and y is one of 8, 9, A, or B
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

  return uuidRegex.test(uuid);
};
/**
 * Builds the complete callback URL for SSO configuration
 * @param baseUrl - The base URL of the application
 * @returns The complete callback URL
 */
export const buildCallbackURL = (baseUrl: string): string => {
  // Ensure baseUrl doesn't end with slash
  const cleanBaseUrl = baseUrl.endsWith("/") ? baseUrl.slice(0, -1) : baseUrl;

  return `${cleanBaseUrl}${LandrayOASSOCallbackRoute}`;
};
