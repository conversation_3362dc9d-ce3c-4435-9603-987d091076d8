import { useLocalStorage } from "@reactivers/hooks";
import { base_url } from "config";
import dayjs from "dayjs";
import minMax from "dayjs/plugin/minMax";
import numeral from "numeral";
import { type } from "ramda";
import { useLayoutEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { userInfoNameInLocalStorage } from "./constants";

export * from "./exports";
export * from "./methods";
export * from "./ssoUtils";

dayjs.extend(minMax);

// eslint-disable-next-line import/prefer-default-export
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(() => matchMedia(query).matches);

  useLayoutEffect(() => {
    const mediaQuery = matchMedia(query);

    function onMediaQueryChange(): void {
      setMatches(mediaQuery.matches);
    }

    mediaQuery.addEventListener("change", onMediaQueryChange);

    return (): void => {
      mediaQuery.removeEventListener("change", onMediaQueryChange);
    };
  }, [query]);

  return matches;
}

// utils.ts

export function handleError(response: Response) {
  if (!response.ok) {
    // TODO: 留着第三方日志记录用
    // throw {
    //   code: response.status,
    //   message: response.statusText
    // } as ResponseError;
  }
}

// 自定义格式化为 RFC3339
export const formatRFC3339 = (date: Date) => {
  return dayjs(date).format("YYYY-MM-DDTHH:mm:ssZ");
};

export const formatDate = (date: Date) => {
  if (
    date === "0001-01-01T00:00:00Z" ||
    date === "0001-01-01T00:00:00" ||
    date === "0001-01-01T00:00:00+08:00" ||
    date === "0001-01-01T00:00:00+00:00" ||
    date === null ||
    date === undefined
  ) {
    return null;
  }
  return dayjs(date).format("YYYY-MM-DD HH:mm:ss");
};

export const formatDateHour = (date: Date) => {
  if (
    date === "0001-01-01T00:00:00Z" ||
    date === "0001-01-01T00:00:00" ||
    date === "0001-01-01T00:00:00+08:00" ||
    date === "0001-01-01T00:00:00+00:00" ||
    date === null ||
    date === undefined
  ) {
    return null;
  }
  return dayjs(date).format("YYYY-MM-DD HH:00:00");
};

export const formatDateDay = (date: Date) => {
  if (
    date === "0001-01-01T00:00:00Z" ||
    date === "0001-01-01T00:00:00" ||
    date === "0001-01-01T00:00:00+08:00" ||
    date === "0001-01-01T00:00:00+00:00" ||
    date === null ||
    date === undefined
  ) {
    return null;
  }
  return dayjs(date).format("YYYY-MM-DD");
};

export const formatArrayImg = (img: string): string => {
  if (!img) {
    return "";
  }
  let imgList = [];
  try {
    imgList = JSON.parse(img);
  } catch (e) {
    imgList.push(img);
  }

  return `${base_url}${imgList?.[0] ?? ""}`;
};

export const formatNumToRate = (num: number): string => {
  if (num === null || num === undefined || isNaN(num)) {
    return "-";
  }
  // 转换数字位百分比，然后保留2位小数
  // return `${(num * 100).toFixed(2)}%`;
  return numeral(num).format("0.00%");
};

export const getBase64 = (file: File): string => {
  const reader = new FileReader();
  return new Promise((resolve, reject) => {
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

const IMAGES_MAP = [
  "attachPicture",
  "processDescription",
  "casePicture",
  "picture",
  "icon",
  "image",
  "images",
  "licenses",
  "attachments",
  "attachment",
  "attachmentList",
  "contracts",
  "imageList",
  "licenseList",
  "contractList",
  "planeFigureList",
  "licenseImageList",
  "backgroundImage",
  "reportPictures",
  "rectifyPictures",
  "acceptPictures",
  "idCardImageList",
  "insuranceImageList",
];
const DATETIME_MAP = ["employmentDate", "shareExpireTime"];

/* 这个函数用于读取后端API数据，并做填充，有几个需要注意的点
1. 当类型为String或者Number的时候，会检查是否在IMAGES_MAP中，如果在的话，会做特殊处理设置xx_Upload字段
  1.1 将字符串转换为JSON数组
  1.2 如果1.1中JSON解析出错，就理解为是单个文件上传的字符串，直接返回
  1.3 其它情况的String或者Number，直接返回，设置新对象
2. 当类型为Object的时候，会取id字段，设置新对象
3. 当类型为Array的时候，会检查是否在IMAGES_MAP中，如果在的话，会做特殊处理，设置xx_Upload字段
  3.1 这里和1中的区别，在于老的接口协议会区分JSON和字符串，新的接口协议统一为JSON
  3.2 其它情况的Array，会设置xxIds字段(老的接口协议)，或者xxIdList字段(新的接口协议)
 */
export const filterEditData = (data: any): any => {
  let newData = {};
  Object.keys(data).map((o: string) => {
    let isOverWritten = false;
    // console.log('o', o, data[o], type(data[o]));
    if (type(data[o]) === "String" || type(data[o]) === "Number") {
      if (IMAGES_MAP.includes(o) && data[o]) {
        try {
          const fieldValue = data[o];
          // const fileName = JSON.parse(fieldValue)[0].split('/').pop()
          newData[`${o}_upload`] = JSON.parse(fieldValue).map((i, key) => ({
            // uid: '1',
            // name: 'demo.jpeg',
            name: i.split("/").pop(),
            status: "success",
            preview: true,
            // fileInstance: new File([new ArrayBuffer(2048)], 'demo.jpeg', { type: 'image/jpeg' }),
            url: i?.includes("data:image") ? i : `${base_url}${i}`,
            uid: uuidv4(),
          }));
        } catch (e) {
          // newData[`${o}_upload`] = [] => 处理单个上传文件，返回字符串的场景
          // console.error(e, data[o]);
          const fileName = data[o].split("/").pop();
          newData[`${o}_upload`] = [
            {
              name: fileName,
              status: "success",
              preview: true,
              url: data[o]?.includes("data:image")
                ? data[o]
                : `${base_url}${data[o]}`,
            },
          ];
        }
      } else if (DATETIME_MAP.includes(o)) {
        if (data[o].startsWith("0001-01-01T")) {
          newData[o] = null;
          isOverWritten = true;
        }
      }
    } else if (type(data[o]) === "Object") {
      newData[`${o}Id`] = data[o]?.id ?? null;
    } else if (type(data[o]) === "Array") {
      if (IMAGES_MAP.includes(o) && data[o]?.length > 0) {
        newData[`${o}_upload`] = data[o].map((i, key) => ({
          name: i.split("/").pop(),
          status: "success",
          preview: true,
          url: i?.includes("data:image") ? i : `${base_url}${i}`,
          uid: uuidv4(),
        }));
      } else {
        // 以下只针对类似{id: xxx, ...}的数据结构有效
        newData[`${o}Ids`] = data[o]?.map((i: any) => i?.id) ?? []; // old style
        newData[`${o}IdList`] = data[o]?.map((i: any) => i?.id) ?? []; // new style
        newData[o.endsWith("s") ? `${o.slice(0, -1)}Ids` : `${o}Ids`] =
          data[o]?.map((i: any) => i?.id) ?? []; // s复数形式 old style;
        newData[o.endsWith("s") ? `${o.slice(0, -1)}IdList` : `${o}IdList`] =
          data[o]?.map((i: any) => i?.id) ?? []; // s复数形式 new style;
        newData[o.endsWith("List") ? `${o.slice(0, -4)}Ids` : `${o}Ids`] =
          data[o]?.map((i: any) => i?.id) ?? []; // List形式 old style;
        newData[o.endsWith("List") ? `${o.slice(0, -4)}IdList` : `${o}IdList`] =
          data[o]?.map((i: any) => i?.id) ?? []; // List形式 old style;
      }
    } else {
      console.log("未知数据类型", o, data[o]);
    }
    if (!isOverWritten) {
      newData[o] = data[o];
    }
  });
  if ("latitude" in data && "longitude" in data) {
    newData["map"] = `${data["longitude"]},${data["latitude"]}`;
  }
  console.log("newData", newData);

  return newData;
};

export const getTourist = () => {
  const customHeadKey = localStorage.getItem("customHeadKey");
  const customHeadValue = localStorage.getItem("customHeadValue");

  if (customHeadKey && customHeadValue) {
    return true;
  }
  return false;
};

export const logoutTourist = () => {
  localStorage.removeItem("customHeadKey");
  localStorage.removeItem("customHeadValue");
};

export const getAdmins = (employeeId: string) => {
  if (getTourist()) {
    return true;
  }

  const admins = ["admin", "superadmin", "vrenadmin"];
  if (admins.includes(employeeId)) {
    return true;
  }

  const devmode_admins = ["gh_336", "gh_335", "gh_9977", "gh_9988"];
  const devmode_hosts = ["localhost", "0.0.0.0"];
  if (
    devmode_admins.includes(employeeId) &&
    devmode_hosts.includes(window.location.hostname)
  ) {
    // console.log(window.location.hostname, '开发模式下关闭权限限制', employeeId);
    return true;
  }

  return false;
};

export const millisecondsOfOnemonth = 1000 * 60 * 60 * 24 * 30; // 30 days

export const getUserInfo = () => {
  const { getItem, removeItem } = useLocalStorage(userInfoNameInLocalStorage);
  const id = getItem()?.userInfo.id;
  const employeeId = getItem()?.userInfo.employeeId;
  const userName = getItem()?.username;

  return { id: id, employeeId: employeeId, userName: userName };
};

export const isUserInList = (userIdList: string[], employeeId: string) => {
  if (!userIdList || !employeeId) {
    return false;
  }
  return userIdList.includes(employeeId);
};

export const isUserInCandidateList = (
  userList: {
    id: string;
    name: string;
  }[],
  employeeId: string
) => {
  if (!userList || !employeeId) {
    return false;
  }
  return userList.some((item) => item.id === employeeId);
};

export const isCurrentUserInCandidateList = (
  userList: {
    id: string;
    name: string;
  }[]
) => {
  const { id, employeeId, userName } = getUserInfo();
  // console.debug("id", id, "employeeId", employeeId, "userName", userName);
  // console.debug("userList", userList);
  return isUserInCandidateList(userList, id);
};

// 设置功能状态
export function setFunctionStatus(functionNumber, isEnabled, status) {
  // 将功能的开关状态设置到对应的位上
  if (isEnabled) {
    status |= 1 << functionNumber;
  } else {
    status &= ~(1 << functionNumber);
  }
  return status;
}

// 判断功能状态
export function isFunctionEnabled(functionNumber, status) {
  // 检查对应位上的状态是否为1
  return (status & (1 << functionNumber)) !== 0;
}
/* 示例用法
var status = 0; // 初始状态，所有功能关闭

status = setFunctionStatus(0, true, status); // 打开功能0
status = setFunctionStatus(1, false, status); // 关闭功能1

console.log(isFunctionEnabled(0, status)); // 输出 true
console.log(isFunctionEnabled(1, status)); // 输出 false
console.log(isFunctionEnabled(2, status)); // 输出 false
*/

export const threeModuleSize = 1024 * 1024 * 1024 * 2; // 512M

export const listPageSizeWithoutPaging = 99999;

export const mediainfoTrackType = {
  general: "General",
  video: "Video",
  audio: "Audio",
  text: "Text",
  image: "Image",
  menu: "Menu",
  other: "Other",
};

export const makeid = (length: number) => {
  let result = "";
  const characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  const charactersLength = characters.length;
  let counter = 0;
  while (counter < length) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
    counter += 1;
  }
  return result;
};

export const renderMarkerList = (
  pointList: { longitude: number; latitude: number }[]
) => {
  return pointList
    ?.map?.((o, key) => {
      return `(${o.longitude}, ${o.latitude})`;
    })
    ?.join?.(", ");
};

export const trainingEditTooltip = (
  entity: string,
  referedOngointPlanList: [any]
) => {
  const planNameList = referedOngointPlanList.map((item) => item.name);
  const tooltipString = `该${entity}正在被“${planNameList}”等${referedOngointPlanList.length}个正在进行的培训计划引用，修改对于它们不生效`;
  return tooltipString;
};
